import 'package:flutter/material.dart';
import '../models/hotspot_models.dart';
import '../services/hotspot_service.dart';

class HotspotProvider extends ChangeNotifier {
  final HotspotService _service = HotspotService();

  // Login state
  bool _isLoggedIn = false;
  bool _isLoading = false;
  String? _error;

  // User data
  StatusResponse? _currentStatus;

  // Form data
  String _username = '';
  String _selectedSpeed = 'normal';
  bool _disableGoogleUpdates = false;

  // Getters
  bool get isLoggedIn => _isLoggedIn;
  bool get isLoading => _isLoading;
  String? get error => _error;
  StatusResponse? get currentStatus => _currentStatus;
  String get username => _username;
  String get selectedSpeed => _selectedSpeed;
  bool get disableGoogleUpdates => _disableGoogleUpdates;

  // Setters
  void setUsername(String value) {
    _username = value;
    notifyListeners();
  }

  void setSelectedSpeed(String value) {
    _selectedSpeed = value;
    notifyListeners();
  }

  void setDisableGoogleUpdates(bool value) {
    _disableGoogleUpdates = value;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Clear error after a delay
  void _clearErrorAfterDelay() {
    Future.delayed(const Duration(seconds: 5), () {
      if (_error != null) {
        _error = null;
        notifyListeners();
      }
    });
  }

  Future<void> login() async {
    if (_username.trim().isEmpty) {
      _error = 'يجب ادخال قيمة صحيحة!';
      notifyListeners();
      return;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Build domain value
      final updateValue = _disableGoogleUpdates ? '_Uoff' : '';
      final domain = _selectedSpeed + updateValue;

      final request = LoginRequest(
        username: _username.trim(),
        domain: domain,
      );

      final response = await _service.login(request);

      if (response.isSuccess) {
        _isLoggedIn = true;
        await _saveLoginState();
        _startStatusUpdates();
      } else if (response.isError) {
        _error = _service.toArabicError(response.error ?? 'خطأ غير معروف');
      } else {
        _error = 'خطأ غير معروف في تسجيل الدخول';
      }
    } catch (e) {
      _error = 'خطأ في الاتصال: ${e.toString()}';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> logout() async {
    _isLoading = true;
    notifyListeners();

    try {
      await _service.logout();
      _isLoggedIn = false;
      _currentStatus = null;
      _service.stopStatusQuery();
      await _clearLoginState();
    } catch (e) {
      _error = 'خطأ في تسجيل الخروج: ${e.toString()}';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void _startStatusUpdates() {
    _service.startStatusQuery((status) {
      _currentStatus = status;
      if (!status.loggedIn) {
        _isLoggedIn = false;
        _clearLoginState();
      }
      notifyListeners();
    });
  }

  Future<void> _saveLoginState() async {
    // TODO: Implement SharedPreferences when available
    // For now, just keep state in memory
    // Login state saved in memory
  }

  Future<void> _clearLoginState() async {
    // TODO: Implement SharedPreferences when available
    // For now, just clear memory state
    // Login state cleared from memory
  }

  Future<void> loadSavedState() async {
    // TODO: Implement SharedPreferences when available
    // For now, start with default state
    _isLoggedIn = false;
    _username = '';
    _selectedSpeed = 'normal';
    _disableGoogleUpdates = false;

    // Check initial login status
    await checkInitialLoginStatus();

    notifyListeners();
  }

  Future<void> checkInitialLoginStatus() async {
    try {
      _isLoading = true;
      notifyListeners();

      final response = await _service.checkInitialLoginStatus();

      if (response.isSuccess) {
        _isLoggedIn = true;
        _startStatusUpdates();
      } else {
        _isLoggedIn = false;
        _currentStatus = null;
      }
    } catch (e) {
      // If check fails, assume not logged in
      _isLoggedIn = false;
      _currentStatus = null;
      // Don't show error for initial check
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Helper methods for UI
  String getFormattedBytes(String? bytes) {
    if (bytes == null || bytes.isEmpty) return '0 بايت';
    return _service.toArabicBytes(bytes);
  }

  String getFormattedTime(String? time) {
    if (time == null || time.isEmpty) return '';
    return _service.toArabicTime(time);
  }

  String getFormattedSpeed(String? speed) {
    if (speed == null || speed.isEmpty) return 'سرعة متوسطة';
    return _service.toArabicSpeed(speed);
  }

  double getBytesPercentage() {
    if (_currentStatus?.remainBytesTotal == null) return 0.0;

    try {
      final remainBytes = int.parse(_currentStatus!.remainBytesTotal!);
      final totalBytes = remainBytes +
          (int.tryParse(_currentStatus!.bytesIn ?? '0') ?? 0) +
          (int.tryParse(_currentStatus!.bytesOut ?? '0') ?? 0);

      if (totalBytes == 0) return 0.0;

      final usedBytes = totalBytes - remainBytes;
      return (usedBytes / totalBytes * 100).clamp(0.0, 100.0);
    } catch (e) {
      return 0.0;
    }
  }

  // Get current speed from status
  String get currentSpeed => _currentStatus?.currentSpeed ?? 'normal';

  // Get current update status
  bool get currentUpdatesDisabled => _currentStatus?.isUpdatesDisabled ?? false;

  // Change speed
  Future<void> changeSpeed(String newSpeed) async {
    if (_currentStatus?.username == null || _isLoading) return;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await _service.changeSettings(
        username: _currentStatus!.username!,
        newSpeed: newSpeed,
        disableUpdates: currentUpdatesDisabled,
      );

      if (response.isSuccess) {
        // Settings changed successfully, status will be updated automatically
        // through the status query loop
      } else if (response.isError) {
        _error =
            _service.toArabicError(response.error ?? 'خطأ في تغيير السرعة');
        _clearErrorAfterDelay();
      } else {
        _error = 'خطأ غير معروف في تغيير السرعة';
        _clearErrorAfterDelay();
      }
    } catch (e) {
      _error = 'خطأ في الاتصال: ${e.toString()}';
      _clearErrorAfterDelay();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Change update settings
  Future<void> changeUpdateSettings(bool disableUpdates) async {
    if (_currentStatus?.username == null || _isLoading) return;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await _service.changeSettings(
        username: _currentStatus!.username!,
        newSpeed: currentSpeed,
        disableUpdates: disableUpdates,
      );

      if (response.isSuccess) {
        // Settings changed successfully, status will be updated automatically
        // through the status query loop
      } else if (response.isError) {
        _error = _service
            .toArabicError(response.error ?? 'خطأ في تغيير إعدادات التحديثات');
        _clearErrorAfterDelay();
      } else {
        _error = 'خطأ غير معروف في تغيير إعدادات التحديثات';
        _clearErrorAfterDelay();
      }
    } catch (e) {
      _error = 'خطأ في الاتصال: ${e.toString()}';
      _clearErrorAfterDelay();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  @override
  void dispose() {
    _service.stopStatusQuery();
    super.dispose();
  }
}
