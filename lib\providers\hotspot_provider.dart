import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/hotspot_models.dart';
import '../services/hotspot_service.dart';

class HotspotProvider extends ChangeNotifier {
  final HotspotService _service = HotspotService();
  
  // Login state
  bool _isLoggedIn = false;
  bool _isLoading = false;
  String? _error;
  
  // User data
  StatusResponse? _currentStatus;
  
  // Form data
  String _username = '';
  String _selectedSpeed = 'normal';
  bool _disableGoogleUpdates = false;

  // Getters
  bool get isLoggedIn => _isLoggedIn;
  bool get isLoading => _isLoading;
  String? get error => _error;
  StatusResponse? get currentStatus => _currentStatus;
  String get username => _username;
  String get selectedSpeed => _selectedSpeed;
  bool get disableGoogleUpdates => _disableGoogleUpdates;

  // Setters
  void setUsername(String value) {
    _username = value;
    notifyListeners();
  }

  void setSelectedSpeed(String value) {
    _selectedSpeed = value;
    notifyListeners();
  }

  void setDisableGoogleUpdates(bool value) {
    _disableGoogleUpdates = value;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  Future<void> login() async {
    if (_username.trim().isEmpty) {
      _error = 'يجب ادخال قيمة صحيحة!';
      notifyListeners();
      return;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Build domain value
      final updateValue = _disableGoogleUpdates ? '_Uoff' : '';
      final domain = _selectedSpeed + updateValue;

      final request = LoginRequest(
        username: _username.trim(),
        domain: domain,
      );

      final response = await _service.login(request);

      if (response.loggedIn) {
        _isLoggedIn = true;
        await _saveLoginState();
        _startStatusUpdates();
      } else {
        _error = _service.toArabicError(response.error ?? 'خطأ غير معروف');
      }
    } catch (e) {
      _error = 'خطأ في الاتصال: ${e.toString()}';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> logout() async {
    _isLoading = true;
    notifyListeners();

    try {
      await _service.logout();
      _isLoggedIn = false;
      _currentStatus = null;
      _service.stopStatusQuery();
      await _clearLoginState();
    } catch (e) {
      _error = 'خطأ في تسجيل الخروج: ${e.toString()}';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void _startStatusUpdates() {
    _service.startStatusQuery((status) {
      _currentStatus = status;
      if (!status.loggedIn) {
        _isLoggedIn = false;
        _clearLoginState();
      }
      notifyListeners();
    });
  }

  Future<void> _saveLoginState() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isLoggedIn', true);
    await prefs.setString('username', _username);
    await prefs.setString('selectedSpeed', _selectedSpeed);
    await prefs.setBool('disableGoogleUpdates', _disableGoogleUpdates);
  }

  Future<void> _clearLoginState() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('isLoggedIn');
    await prefs.remove('username');
    await prefs.remove('selectedSpeed');
    await prefs.remove('disableGoogleUpdates');
  }

  Future<void> loadSavedState() async {
    final prefs = await SharedPreferences.getInstance();
    _isLoggedIn = prefs.getBool('isLoggedIn') ?? false;
    _username = prefs.getString('username') ?? '';
    _selectedSpeed = prefs.getString('selectedSpeed') ?? 'normal';
    _disableGoogleUpdates = prefs.getBool('disableGoogleUpdates') ?? false;

    if (_isLoggedIn) {
      _startStatusUpdates();
    }
    
    notifyListeners();
  }

  // Helper methods for UI
  String getFormattedBytes(String? bytes) {
    if (bytes == null || bytes.isEmpty) return '0 بايت';
    return _service.toArabicBytes(bytes);
  }

  String getFormattedTime(String? time) {
    if (time == null || time.isEmpty) return '';
    return _service.toArabicTime(time);
  }

  String getFormattedSpeed(String? speed) {
    if (speed == null || speed.isEmpty) return 'سرعة متوسطة';
    return _service.toArabicSpeed(speed);
  }

  double getBytesPercentage() {
    if (_currentStatus?.remainBytesTotal == null) return 0.0;
    
    try {
      final remainBytes = int.parse(_currentStatus!.remainBytesTotal!);
      final totalBytes = remainBytes + 
          (int.tryParse(_currentStatus!.bytesIn ?? '0') ?? 0) +
          (int.tryParse(_currentStatus!.bytesOut ?? '0') ?? 0);
      
      if (totalBytes == 0) return 0.0;
      
      final usedBytes = totalBytes - remainBytes;
      return (usedBytes / totalBytes * 100).clamp(0.0, 100.0);
    } catch (e) {
      return 0.0;
    }
  }

  @override
  void dispose() {
    _service.stopStatusQuery();
    super.dispose();
  }
}
