import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/hotspot_provider.dart';
import '../models/hotspot_models.dart';

class SettingsCardWidget extends StatefulWidget {
  const SettingsCardWidget({super.key});

  @override
  State<SettingsCardWidget> createState() => _SettingsCardWidgetState();
}

class _SettingsCardWidgetState extends State<SettingsCardWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Card(
          elevation: 8,
          shadowColor: Colors.black26,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: const LinearGradient(
                colors: [Colors.white, Color(0xFFF8F9FA)],
              ),
            ),
            child: Consumer<HotspotProvider>(
              builder: (context, provider, child) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [Color(0xFF68DAFC), Color(0xFF29B6F6)],
                            ),
                            borderRadius: BorderRadius.circular(25),
                          ),
                          child: const Icon(
                            Icons.settings,
                            color: Colors.white,
                            size: 25,
                          ),
                        ),
                        const SizedBox(width: 16),
                        const Expanded(
                          child: Text(
                            'إعدادات الاتصال',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF2E3A59),
                            ),
                            textAlign: TextAlign.right,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    _buildSpeedSelector(provider),
                    const SizedBox(height: 16),
                    _buildUpdateToggle(provider),
                    if (provider.error != null) ...[
                      const SizedBox(height: 16),
                      _buildErrorMessage(provider.error!),
                    ],
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSpeedSelector(HotspotProvider provider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.speed, color: Colors.grey[600], size: 20),
              const SizedBox(width: 8),
              const Expanded(
                child: Text(
                  'السرعة الحالية',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF2E3A59),
                  ),
                  textAlign: TextAlign.right,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          DropdownButtonFormField<String>(
            value: provider.currentSpeed,
            decoration: InputDecoration(
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Color(0xFF68DAFC), width: 2),
              ),
              filled: true,
              fillColor: Colors.white,
            ),
            items: SpeedOption.options.map((option) {
              return DropdownMenuItem<String>(
                value: option.value,
                child: Align(
                  alignment: Alignment.centerRight,
                  child: Text(
                    option.name,
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
              );
            }).toList(),
            onChanged: provider.isLoading ? null : (value) {
              if (value != null && value != provider.currentSpeed) {
                _showChangeConfirmation(
                  context,
                  'تغيير السرعة',
                  'هل أنت متأكد من تغيير السرعة؟ سيتم قطع الاتصال مؤقتاً.',
                  () => provider.changeSpeed(value),
                );
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildUpdateToggle(HotspotProvider provider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          Icon(
            provider.currentUpdatesDisabled ? Icons.update_disabled : Icons.update,
            color: provider.currentUpdatesDisabled ? Colors.orange[600] : Colors.green[600],
            size: 20,
          ),
          const SizedBox(width: 8),
          const Expanded(
            child: Text(
              'تحديثات جوجل',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Color(0xFF2E3A59),
              ),
              textAlign: TextAlign.right,
            ),
          ),
          Switch(
            value: !provider.currentUpdatesDisabled,
            onChanged: provider.isLoading ? null : (value) {
              _showChangeConfirmation(
                context,
                value ? 'تفعيل التحديثات' : 'إيقاف التحديثات',
                value 
                    ? 'هل تريد تفعيل تحديثات جوجل؟ سيتم قطع الاتصال مؤقتاً.'
                    : 'هل تريد إيقاف تحديثات جوجل؟ سيتم قطع الاتصال مؤقتاً.',
                () => provider.changeUpdateSettings(!value),
              );
            },
            activeColor: const Color(0xFF68DAFC),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorMessage(String error) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red[50],
        border: Border.all(color: Colors.red[200]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: Colors.red[600], size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              error,
              textAlign: TextAlign.right,
              style: TextStyle(
                color: Colors.red[700],
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showChangeConfirmation(
    BuildContext context,
    String title,
    String message,
    VoidCallback onConfirm,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            title,
            textAlign: TextAlign.right,
            style: const TextStyle(fontSize: 18),
          ),
          content: Text(
            message,
            textAlign: TextAlign.right,
            style: const TextStyle(fontSize: 14),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                onConfirm();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF68DAFC),
                foregroundColor: Colors.white,
              ),
              child: const Text('تأكيد'),
            ),
          ],
        );
      },
    );
  }
}
