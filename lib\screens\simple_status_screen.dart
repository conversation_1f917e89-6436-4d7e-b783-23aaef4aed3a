import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math' as math;
import 'simple_login_screen.dart';

class SimpleStatusScreen extends StatefulWidget {
  final String username;
  final String speed;
  final bool disableUpdates;

  const SimpleStatusScreen({
    super.key,
    required this.username,
    required this.speed,
    required this.disableUpdates,
  });

  @override
  State<SimpleStatusScreen> createState() => _SimpleStatusScreenState();
}

class _SimpleStatusScreenState extends State<SimpleStatusScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  Timer? _statusTimer;
  
  // Mock data for demonstration
  String _bytesIn = '1048576'; // 1 MB
  String _bytesOut = '2097152'; // 2 MB
  String _remainBytes = '523386189'; // ~500 MB
  String _uptime = '1h23m45s';
  String _sessionTimeLeft = '2h36m15s';
  String _ip = '**********';

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));
    
    _animationController.forward();
    _startMockStatusUpdates();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _statusTimer?.cancel();
    super.dispose();
  }

  void _startMockStatusUpdates() {
    // Simulate periodic status updates
    _statusTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (mounted) {
        setState(() {
          // Simulate increasing data usage
          final bytesInInt = int.parse(_bytesIn) + math.Random().nextInt(100000);
          final bytesOutInt = int.parse(_bytesOut) + math.Random().nextInt(100000);
          _bytesIn = bytesInInt.toString();
          _bytesOut = bytesOutInt.toString();
          
          // Update uptime
          final uptimeParts = _uptime.split('h');
          if (uptimeParts.length == 2) {
            final hours = int.tryParse(uptimeParts[0]) ?? 1;
            final minutesParts = uptimeParts[1].split('m');
            final minutes = int.tryParse(minutesParts[0]) ?? 23;
            final seconds = int.tryParse(minutesParts[1].split('s')[0]) ?? 45;
            
            var newSeconds = seconds + 5;
            var newMinutes = minutes;
            var newHours = hours;
            
            if (newSeconds >= 60) {
              newSeconds = 0;
              newMinutes++;
            }
            if (newMinutes >= 60) {
              newMinutes = 0;
              newHours++;
            }
            
            _uptime = '${newHours}h${newMinutes}m${newSeconds}s';
          }
        });
      }
    });
  }

  String _getSpeedName(String speed) {
    const speedMap = {
      'economic': 'سرعة اقتصادية',
      'normal': 'سرعة متوسطة',
      'high': 'سرعة عالية',
      'very': 'سرعة مفتوحة',
      'gaming': 'سرعة ألعاب أون لاين',
    };
    return speedMap[speed] ?? 'سرعة متوسطة';
  }

  String _formatBytes(String bytesStr) {
    try {
      final bytes = int.parse(bytesStr);
      if (bytes < 1024) {
        return '$bytes بايت';
      } else if (bytes < 1048576) {
        return '${(bytes / 1024).round()} كيلوبايت';
      } else if (bytes < 1073741824) {
        return '${(bytes / 1048576).round()} ميجابايت';
      } else {
        return '${(bytes / 1073741824).toStringAsFixed(2)} جيجابايت';
      }
    } catch (e) {
      return bytesStr;
    }
  }

  String _formatTime(String timeStr) {
    return timeStr
        .replaceAll('h', ' ساعة ')
        .replaceAll('m', ' دقيقة ')
        .replaceAll('s', ' ثانية');
  }

  double _getUsagePercentage() {
    try {
      final totalUsed = int.parse(_bytesIn) + int.parse(_bytesOut);
      final totalAvailable = int.parse(_remainBytes) + totalUsed;
      return totalAvailable > 0 ? (totalUsed / totalAvailable * 100).clamp(0.0, 100.0) : 0.0;
    } catch (e) {
      return 25.0; // Default value for demo
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF68DAFC),
              Color(0xFF4FC3F7),
              Color(0xFF29B6F6),
            ],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: CustomScrollView(
                slivers: [
                  _buildAppBar(),
                  SliverPadding(
                    padding: const EdgeInsets.all(16.0),
                    sliver: SliverList(
                      delegate: SliverChildListDelegate([
                        _buildUserInfoCard(),
                        const SizedBox(height: 16),
                        _buildDataUsageCard(),
                        const SizedBox(height: 16),
                        _buildStatsGrid(),
                        const SizedBox(height: 16),
                        _buildLogoutButton(),
                        const SizedBox(height: 20),
                      ]),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: FlexibleSpaceBar(
        title: const Text(
          'حالة الاتصال',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        background: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFF68DAFC),
                Color(0xFF29B6F6),
              ],
            ),
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh, color: Colors.white),
          onPressed: () {
            // Refresh status manually
            setState(() {});
          },
        ),
      ],
    );
  }

  Widget _buildUserInfoCard() {
    return Card(
      elevation: 8,
      shadowColor: Colors.black26,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: const LinearGradient(
            colors: [Colors.white, Color(0xFFF8F9FA)],
          ),
        ),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFF68DAFC), Color(0xFF29B6F6)],
                    ),
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: const Icon(
                    Icons.person,
                    color: Colors.white,
                    size: 30,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        widget.username,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF2E3A59),
                        ),
                        textAlign: TextAlign.right,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getSpeedName(widget.speed),
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.right,
                      ),
                      if (widget.disableUpdates) ...[
                        const SizedBox(height: 4),
                        Text(
                          'تم إيقاف تحديثات جوجل',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.orange[600],
                          ),
                          textAlign: TextAlign.right,
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.check_circle, color: Colors.green[600], size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'متصل بالشبكة - $_ip',
                    style: TextStyle(
                      color: Colors.green[700],
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataUsageCard() {
    final percentage = _getUsagePercentage();
    
    return Card(
      elevation: 8,
      shadowColor: Colors.black26,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: const LinearGradient(
            colors: [Colors.white, Color(0xFFF8F9FA)],
          ),
        ),
        child: Column(
          children: [
            const Text(
              'استهلاك البيانات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2E3A59),
              ),
            ),
            const SizedBox(height: 20),
            _buildCircularProgress(percentage),
            const SizedBox(height: 16),
            Text(
              _formatBytes(_remainBytes),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2E3A59),
              ),
            ),
            Text(
              'متبقي',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCircularProgress(double percentage) {
    return SizedBox(
      width: 120,
      height: 120,
      child: Stack(
        alignment: Alignment.center,
        children: [
          SizedBox(
            width: 120,
            height: 120,
            child: CircularProgressIndicator(
              value: percentage / 100,
              strokeWidth: 8,
              valueColor: AlwaysStoppedAnimation<Color>(
                percentage > 80 ? Colors.red : const Color(0xFF68DAFC),
              ),
              backgroundColor: Colors.grey[300],
            ),
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '${percentage.toInt()}%',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: percentage > 80 ? Colors.red : const Color(0xFF68DAFC),
                ),
              ),
              Text(
                'مستخدم',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatsGrid() {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.2,
      children: [
        _buildStatCard(
          'البيانات المرسلة',
          _formatBytes(_bytesOut),
          Icons.upload,
          Colors.orange,
        ),
        _buildStatCard(
          'البيانات المستلمة',
          _formatBytes(_bytesIn),
          Icons.download,
          Colors.green,
        ),
        _buildStatCard(
          'وقت الجلسة',
          _formatTime(_uptime),
          Icons.access_time,
          Colors.blue,
        ),
        _buildStatCard(
          'الوقت المتبقي',
          _formatTime(_sessionTimeLeft),
          Icons.timer,
          Colors.purple,
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 8,
      shadowColor: color.withValues(alpha: 0.3),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              color.withValues(alpha: 0.05),
            ],
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Icon(
                icon,
                color: color,
                size: 28,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            Text(
              value.isEmpty ? '0' : value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLogoutButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: () {
          _showLogoutDialog();
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red[400],
          foregroundColor: Colors.white,
          elevation: 8,
          shadowColor: Colors.red.withValues(alpha: 0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: const Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.logout),
            SizedBox(width: 8),
            Text(
              'تسجيل الخروج',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Text(
            'تسجيل الخروج',
            textAlign: TextAlign.right,
          ),
          content: const Text(
            'هل أنت متأكد من أنك تريد تسجيل الخروج؟',
            textAlign: TextAlign.right,
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _logout();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red[400],
                foregroundColor: Colors.white,
              ),
              child: const Text('تسجيل الخروج'),
            ),
          ],
        );
      },
    );
  }

  void _logout() {
    _statusTimer?.cancel();
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => const SimpleLoginScreen(),
      ),
    );
  }
}
