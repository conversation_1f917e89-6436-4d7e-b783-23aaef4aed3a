import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'providers/hotspot_provider.dart';
import 'screens/login_screen.dart';
import 'screens/status_screen.dart';
import 'screens/splash_screen.dart';

void main() {
  runApp(const SkyNetApp());
}

class SkyNetApp extends StatelessWidget {
  const SkyNetApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => HotspotProvider(),
      child: MaterialApp(
        title: 'SkyNet - هوت سبوت ميكروتيك',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          primarySwatch: Colors.blue,
          primaryColor: const Color(0xFF68DAFC),
          fontFamily: 'Arial',
          textTheme: const TextTheme(
            bodyLarge: TextStyle(fontFamily: 'Arial'),
            bodyMedium: TextStyle(fontFamily: 'Arial'),
            displayLarge: TextStyle(fontFamily: 'Arial'),
            displayMedium: TextStyle(fontFamily: 'Arial'),
            displaySmall: TextStyle(fontFamily: 'Arial'),
            headlineLarge: TextStyle(fontFamily: 'Arial'),
            headlineMedium: TextStyle(fontFamily: 'Arial'),
            headlineSmall: TextStyle(fontFamily: 'Arial'),
            titleLarge: TextStyle(fontFamily: 'Arial'),
            titleMedium: TextStyle(fontFamily: 'Arial'),
            titleSmall: TextStyle(fontFamily: 'Arial'),
            labelLarge: TextStyle(fontFamily: 'Arial'),
            labelMedium: TextStyle(fontFamily: 'Arial'),
            labelSmall: TextStyle(fontFamily: 'Arial'),
            bodySmall: TextStyle(fontFamily: 'Arial'),
          ),
          colorScheme: ColorScheme.fromSeed(
            seedColor: const Color(0xFF68DAFC),
            brightness: Brightness.light,
          ),
          useMaterial3: true,
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              elevation: 8,
              shadowColor: const Color(0xFF68DAFC).withValues(alpha: 0.3),
            ),
          ),
          cardTheme: CardTheme(
            elevation: 8,
            shadowColor: Colors.black26,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
          ),
        ),
        home: const AppWrapper(),
      ),
    );
  }
}

class AppWrapper extends StatefulWidget {
  const AppWrapper({super.key});

  @override
  State<AppWrapper> createState() => _AppWrapperState();
}

class _AppWrapperState extends State<AppWrapper> {
  bool _isInitializing = true;

  @override
  void initState() {
    super.initState();
    // Load saved state when app starts
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeApp();
    });
  }

  Future<void> _initializeApp() async {
    await context.read<HotspotProvider>().loadSavedState();
    if (mounted) {
      setState(() {
        _isInitializing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isInitializing) {
      return const SplashScreen();
    }

    return Consumer<HotspotProvider>(
      builder: (context, provider, child) {
        // Show splash screen while checking initial status
        if (provider.isLoading &&
            !provider.isLoggedIn &&
            provider.error == null) {
          return const SplashScreen();
        }

        // Show login screen if not logged in, otherwise show status screen
        if (provider.isLoggedIn) {
          return const StatusScreen();
        } else {
          return const LoginScreen();
        }
      },
    );
  }
}
