import 'package:flutter/material.dart';
import 'screens/simple_login_screen.dart';

void main() {
  runApp(const SkyNetApp());
}

class SkyNetApp extends StatelessWidget {
  const SkyNetApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'SkyNet - هوت سبوت ميكروتيك',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.blue,
        primaryColor: const Color(0xFF68DAFC),
        fontFamily: 'Arial',
        textTheme: const TextTheme(
          bodyLarge: TextStyle(fontFamily: 'Arial'),
          bodyMedium: TextStyle(fontFamily: 'Arial'),
          displayLarge: TextStyle(fontFamily: 'Arial'),
          displayMedium: TextStyle(fontFamily: 'Arial'),
          displaySmall: TextStyle(fontFamily: 'Arial'),
          headlineLarge: TextStyle(fontFamily: 'Arial'),
          headlineMedium: TextStyle(fontFamily: 'Arial'),
          headlineSmall: TextStyle(fontFamily: 'Arial'),
          titleLarge: TextStyle(fontFamily: 'Arial'),
          titleMedium: TextStyle(fontFamily: 'Arial'),
          titleSmall: TextStyle(fontFamily: 'Arial'),
          labelLarge: TextStyle(fontFamily: 'Arial'),
          labelMedium: TextStyle(fontFamily: 'Arial'),
          labelSmall: TextStyle(fontFamily: 'Arial'),
          bodySmall: TextStyle(fontFamily: 'Arial'),
        ),
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF68DAFC),
          brightness: Brightness.light,
        ),
        useMaterial3: true,
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            elevation: 8,
            shadowColor: const Color(0xFF68DAFC).withValues(alpha: 0.3),
          ),
        ),
        cardTheme: CardTheme(
          elevation: 8,
          shadowColor: Colors.black26,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
      ),
      home: const SimpleLoginScreen(),
    );
  }
}
