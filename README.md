# SkyNet - تطبيق تسجيل الدخول لهوت سبوت ميكروتيك

تطبيق Flutter أنيق وعصري لإدارة عمليات تسجيل الدخول لهوت سبوت ميكروتيك وعرض إحصائيات المستخدم.

## المميزات

- 🎨 تصميم أنيق وعصري بالألوان المطلوبة (#68DAFC)
- 🔐 نموذج تسجيل دخول متكامل مع خيارات السرعة
- 📊 عرض إحصائيات المستخدم في الوقت الفعلي
- ⚙️ خيار إيقاف تحديثات جوجل
- 🌐 دعم كامل للغة العربية
- 📱 واجهة مستخدم متجاوبة

## خيارات السرعة المتاحة

- سرعة اقتصادية (economic)
- سرعة متوسطة (normal)
- سرعة عالية (high)
- سرعة مفتوحة (very)
- سرعة ألعاب أون لاين (gaming)

## كيفية العمل

يقوم التطبيق بإرسال طلبات HTTP إلى عنوان الهوت سبوت (http://10.10.10.1) مع المعاملات التالية:

### تسجيل الدخول
```
/login?username={اسم_المستخدم}&password=&domain={السرعة}{إيقاف_التحديثات}&var=callBack
```

### الحصول على الحالة
```
/status?var=callBack
```

### تسجيل الخروج
```
/logout?var=callBack
```

## البنية الحالية

التطبيق حالياً يعمل في وضع العرض التوضيحي (Demo Mode) بدون الاتصال الفعلي بالهوت سبوت.

### الملفات الرئيسية:
- `lib/main.dart` - نقطة البداية للتطبيق
- `lib/screens/simple_login_screen.dart` - شاشة تسجيل الدخول
- `lib/screens/simple_status_screen.dart` - شاشة عرض الحالة والإحصائيات

## التشغيل

```bash
flutter run -d windows
```

## إضافة الوظائف الكاملة

لتفعيل الاتصال الفعلي بالهوت سبوت، قم بإضافة التبعيات التالية إلى `pubspec.yaml`:

```yaml
dependencies:
  http: ^1.1.0
  provider: ^6.1.1
  shared_preferences: ^2.2.2
```

ثم استخدم الملفات التالية بدلاً من النسخة المبسطة:
- `lib/services/hotspot_service.dart` - خدمة الاتصال بالهوت سبوت
- `lib/providers/hotspot_provider.dart` - إدارة حالة التطبيق
- `lib/models/hotspot_models.dart` - نماذج البيانات
- `lib/screens/login_screen.dart` - شاشة تسجيل الدخول الكاملة
- `lib/screens/status_screen.dart` - شاشة الحالة الكاملة

## الألوان المستخدمة

- اللون الأساسي: `#68DAFC`
- الألوان المتدرجة: `#4FC3F7`, `#29B6F6`

## المتطلبات

- Flutter SDK
- Dart SDK
- Windows (للتشغيل على Windows)

## الترخيص

هذا المشروع مخصص لإدارة هوت سبوت ميكروتيك.
