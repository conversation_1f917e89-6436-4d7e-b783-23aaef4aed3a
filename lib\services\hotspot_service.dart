import 'dart:convert';
import 'dart:async';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../models/hotspot_models.dart';

class HotspotService {
  static const String baseUrl = 'http://10.10.10.1';

  // Singleton pattern
  static final HotspotService _instance = HotspotService._internal();
  factory HotspotService() => _instance;
  HotspotService._internal();

  Timer? _statusTimer;

  Future<Map<String, dynamic>> _getRequest(String endpoint) async {
    try {
      final uri = Uri.parse('$baseUrl$endpoint');
      print('Making request to: $uri'); // Debug log

      final response = await http.get(
        uri,
        headers: {
          'User-Agent': 'SkyNet-Hotspot-Client/1.0',
          'Accept': 'application/json, text/plain, */*',
        },
      ).timeout(
        const Duration(seconds: 15),
        onTimeout: () {
          throw TimeoutException(
              'Request timeout after 15 seconds', const Duration(seconds: 15));
        },
      );

      print('Response status: ${response.statusCode}'); // Debug log
      print('Response body: ${response.body}'); // Debug log

      if (response.statusCode >= 200 && response.statusCode < 400) {
        try {
          return json.decode(response.body);
        } catch (e) {
          // If JSON parsing fails, try to handle as plain text response
          print('JSON parsing failed, handling as text: $e');
          return {
            'error': 'Invalid response format',
            'raw_response': response.body,
            'action': 'onLoginError'
          };
        }
      } else {
        throw Exception(
            'HTTP ${response.statusCode}: ${response.reasonPhrase}');
      }
    } on TimeoutException catch (e) {
      print('Timeout error: $e');
      throw Exception(
          'Connection timeout - please check your network connection');
    } on SocketException catch (e) {
      print('Socket error: $e');
      throw Exception(
          'Network error - cannot reach hotspot server (10.10.10.1)');
    } catch (e) {
      print('General error: $e');
      throw Exception('Network error: $e');
    }
  }

  Future<LoginResponse> login(LoginRequest request) async {
    final queryParams = request.toQueryParams();
    final queryString = queryParams.entries
        .map((e) =>
            '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
        .join('&');

    final endpoint = '/login?$queryString';
    final response = await _getRequest(endpoint);
    return LoginResponse.fromJson(response);
  }

  Future<StatusResponse> getStatus() async {
    const endpoint = '/status?var=callBack';
    final response = await _getRequest(endpoint);
    return StatusResponse.fromJson(response);
  }

  Future<LogoutResponse> logout({bool eraseCookie = false}) async {
    final endpoint = eraseCookie
        ? '/logout?erase-cookie=yes&var=callBack'
        : '/logout?var=callBack';
    final response = await _getRequest(endpoint);
    return LogoutResponse.fromJson(response);
  }

  void startStatusQuery(Function(StatusResponse) onStatusUpdate) {
    _statusTimer?.cancel();
    _statusTimer = Timer.periodic(const Duration(seconds: 1), (timer) async {
      try {
        final status = await getStatus();
        onStatusUpdate(status);

        // Stop timer if user is logged out
        if (!status.loggedIn) {
          timer.cancel();
        }
      } catch (e) {
        // Handle error silently or notify callback
        print('Status query error: $e');
      }
    });
  }

  void stopStatusQuery() {
    _statusTimer?.cancel();
    _statusTimer = null;
  }

  String toArabicBytes(String bytesStr) {
    if (bytesStr.isEmpty) return '';

    try {
      final bytes = int.parse(bytesStr);
      if (bytes < 1024) {
        return '$bytes بايت';
      } else if (bytes < 1048576) {
        return '${(bytes / 1024).round()} كيلوبايت';
      } else if (bytes < 1073741824) {
        return '${(bytes / 1048576).round()} ميجابايت';
      } else {
        return '${(bytes / 1073741824).toStringAsFixed(2)} جيجابايت';
      }
    } catch (e) {
      return bytesStr;
    }
  }

  String toArabicTime(String timeStr) {
    if (timeStr.isEmpty) return '';

    String result = '';
    String temp = timeStr;

    // Days
    if (temp.contains('d')) {
      final parts = temp.split('d');
      if (parts[0].isNotEmpty) {
        result += '${parts[0]} يوم ';
      }
      temp = parts[1];
    }

    // Hours
    if (temp.contains('h')) {
      final parts = temp.split('h');
      if (parts[0].isNotEmpty) {
        result += '${parts[0]} ساعة ';
      }
      temp = parts[1];
    }

    // Minutes
    if (temp.contains('m')) {
      final parts = temp.split('m');
      if (parts[0].isNotEmpty) {
        result += '${parts[0]} دقيقة ';
      }
      temp = parts[1];
    }

    // Seconds
    if (temp.contains('s')) {
      final parts = temp.split('s');
      if (parts[0].isNotEmpty) {
        result += '${parts[0]} ثانية ';
      }
    }

    return result.trim();
  }

  String toArabicError(String error) {
    final errorMap = {
      'user&not found': 'لقد ادخلت الكرت بطريقة غير صحيحة، أو ربما قد أنتهت',
      'simultaneous session limit reached|no more sessions are allowed':
          'المعذرة ، هذا الكرت مستخدم حالياً في جهاز آخر',
      'invalid password': 'تاكد من كتابة كلمة المرور بشكل صحيح',
      'uptime limit reached|No more online time|uptime limit':
          'عذراً لقد انتهى الوقت المتاح لك',
      'traffic limit|transfer limit reached': 'لقد انتهى رصيد هذا الكرت',
      'invalid username or password|not found':
          'لقد ادخلت اسم المستخدم بطريقة غير صحيحة، الرجاء المحاولة مرة اخرى',
      'no valid profile found': 'لقد انتهت صلاحية هذا الكرت',
      'invalid Calling-Station-Id': 'هذا الكرت مقترن بجهاز آخر!',
      'server&is&not&responding':
          'يرجى الانتظار، يتم الآن اعادة تشغيل الشبكة، هذه العملية قد تستغرق بعض الوقت',
      'web&browser&did&not&send': 'يرجى محاولة ادخال الكرت مرة اخرى',
    };

    String arabicError = 'حصل خطأ: $error';

    for (final entry in errorMap.entries) {
      final key = entry.key;
      final value = entry.value;

      if (key.contains('&')) {
        final parts = key.split('&');
        bool allMatch = true;
        for (final part in parts) {
          if (!error.contains(part)) {
            allMatch = false;
            break;
          }
        }
        if (allMatch) {
          arabicError = value;
          break;
        }
      } else if (key.contains('|')) {
        final parts = key.split('|');
        for (final part in parts) {
          if (error.contains(part)) {
            arabicError = value;
            break;
          }
        }
      } else if (error.contains(key)) {
        arabicError = value;
        break;
      }
    }

    return arabicError;
  }

  String toArabicSpeed(String speed) {
    if (speed.isEmpty) return 'سرعة متوسطة';

    for (final option in SpeedOption.options) {
      if (option.value == speed) {
        return option.name;
      }
    }
    return 'سرعة متوسطة';
  }
}
