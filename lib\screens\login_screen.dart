import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/hotspot_provider.dart';
import '../models/hotspot_models.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> with TickerProviderStateMixin {
  final _usernameController = TextEditingController();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF68DAFC),
              Color(0xFF4FC3F7),
              Color(0xFF29B6F6),
            ],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24.0),
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: Card(
                    elevation: 20,
                    shadowColor: Colors.black26,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Container(
                      padding: const EdgeInsets.all(32.0),
                      constraints: const BoxConstraints(maxWidth: 400),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _buildHeader(),
                          const SizedBox(height: 40),
                          _buildLoginForm(),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFF68DAFC), Color(0xFF29B6F6)],
            ),
            borderRadius: BorderRadius.circular(40),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF68DAFC).withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: const Icon(
            Icons.wifi,
            size: 40,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 20),
        const Text(
          'تسجيل الدخول',
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: Color(0xFF2E3A59),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'أدخل بياناتك للاتصال بالشبكة',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildLoginForm() {
    return Consumer<HotspotProvider>(
      builder: (context, provider, child) {
        return Column(
          children: [
            _buildUsernameField(provider),
            const SizedBox(height: 20),
            _buildSpeedDropdown(provider),
            const SizedBox(height: 20),
            _buildGoogleUpdatesCheckbox(provider),
            const SizedBox(height: 30),
            _buildLoginButton(provider),
            if (provider.error != null) ...[
              const SizedBox(height: 20),
              _buildErrorMessage(provider.error!),
            ],
          ],
        );
      },
    );
  }

  Widget _buildUsernameField(HotspotProvider provider) {
    return TextFormField(
      controller: _usernameController,
      textAlign: TextAlign.right,
      style: const TextStyle(fontSize: 16),
      decoration: InputDecoration(
        labelText: 'اسم المستخدم',
        hintText: 'أدخل اسم المستخدم',
        prefixIcon: const Icon(Icons.person, color: Color(0xFF68DAFC)),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF68DAFC), width: 2),
        ),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      onChanged: provider.setUsername,
    );
  }

  Widget _buildSpeedDropdown(HotspotProvider provider) {
    return DropdownButtonFormField<String>(
      value: provider.selectedSpeed,
      decoration: InputDecoration(
        labelText: 'اختيار السرعة',
        prefixIcon: const Icon(Icons.speed, color: Color(0xFF68DAFC)),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Color(0xFF68DAFC), width: 2),
        ),
        filled: true,
        fillColor: Colors.grey[50],
      ),
      items: SpeedOption.options.map((option) {
        return DropdownMenuItem<String>(
          value: option.value,
          child: Align(
            alignment: Alignment.centerRight,
            child: Text(
              option.name,
              style: const TextStyle(fontSize: 16),
            ),
          ),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          provider.setSelectedSpeed(value);
        }
      },
    );
  }

  Widget _buildGoogleUpdatesCheckbox(HotspotProvider provider) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(12),
        color: Colors.grey[50],
      ),
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Checkbox(
            value: provider.disableGoogleUpdates,
            onChanged: (value) {
              provider.setDisableGoogleUpdates(value ?? false);
            },
            activeColor: const Color(0xFF68DAFC),
          ),
          const Expanded(
            child: Text(
              'إيقاف تحديثات جوجل',
              textAlign: TextAlign.right,
              style: TextStyle(fontSize: 16),
            ),
          ),
          const Icon(Icons.update_disabled, color: Color(0xFF68DAFC)),
        ],
      ),
    );
  }

  Widget _buildLoginButton(HotspotProvider provider) {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: provider.isLoading ? null : () {
          provider.clearError();
          provider.setUsername(_usernameController.text);
          provider.login();
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF68DAFC),
          foregroundColor: Colors.white,
          elevation: 8,
          shadowColor: const Color(0xFF68DAFC).withOpacity(0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: provider.isLoading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
            : const Text(
                'تسجيل الدخول',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }

  Widget _buildErrorMessage(String error) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red[50],
        border: Border.all(color: Colors.red[200]!),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: Colors.red[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              error,
              textAlign: TextAlign.right,
              style: TextStyle(
                color: Colors.red[700],
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
