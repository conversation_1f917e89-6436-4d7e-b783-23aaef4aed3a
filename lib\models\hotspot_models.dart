class LoginRequest {
  final String username;
  final String domain;

  LoginRequest({
    required this.username,
    required this.domain,
  });

  Map<String, String> toQueryParams() {
    return {
      'username': username,
      'password': '',
      'domain': domain,
      'var': 'callBack',
    };
  }
}

class LoginResponse {
  final bool loggedIn;
  final String linkLoginOnly;
  final String? error;
  final String? errorOrig;
  final String action;

  LoginResponse({
    required this.loggedIn,
    required this.linkLoginOnly,
    this.error,
    this.errorOrig,
    required this.action,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      loggedIn: json['logged_in'] == 'yes',
      linkLoginOnly: json['link_login_only'] ?? '',
      error: json['error'],
      errorOrig: json['error_orig'],
      action: json['action'] ?? '',
    );
  }

  // Helper method to check if this is a successful login response
  bool get isSuccess => loggedIn && action == 'onLoggedIn';

  // Helper method to check if this is an error response
  bool get isError => !loggedIn && action == 'onLoginError' && error != null;
}

class StatusResponse {
  final bool loggedIn;
  final String? ip;
  final String? username;
  final String? sps;
  final String? bytesIn;
  final String? bytesOut;
  final String? remainBytesTotal;
  final String? sessionTimeLeft;
  final String? uptime;
  final String action;

  StatusResponse({
    required this.loggedIn,
    this.ip,
    this.username,
    this.sps,
    this.bytesIn,
    this.bytesOut,
    this.remainBytesTotal,
    this.sessionTimeLeft,
    this.uptime,
    required this.action,
  });

  factory StatusResponse.fromJson(Map<String, dynamic> json) {
    return StatusResponse(
      loggedIn: json['logged_in'] == 'yes',
      ip: json['ip'],
      username: json['username'],
      sps: json['sps'],
      bytesIn: json['bytes_in'],
      bytesOut: json['bytes_out'],
      remainBytesTotal: json['remain_bytes_total'],
      sessionTimeLeft: json['session_time_left'],
      uptime: json['uptime'],
      action: json['action'] ?? '',
    );
  }

  // Helper methods to extract speed and update status from sps field
  String get currentSpeed {
    if (sps == null || sps!.isEmpty) return 'normal';
    final parts = sps!.split('_');
    return parts.isNotEmpty ? parts[0] : 'normal';
  }

  String get updateStatus {
    if (sps == null || sps!.isEmpty) return '';
    final parts = sps!.split('_');
    return parts.length > 1 ? parts[1] : '';
  }

  bool get isUpdatesDisabled => updateStatus == 'Uoff';
}

class LogoutResponse {
  final bool loggedIn;
  final String linkLoginOnly;
  final String? linkLogout;
  final String? ip;
  final String? bytesIn;
  final String? bytesInNice;
  final String? username;
  final String? bytesOut;
  final String? sps;
  final String? bytesOutNice;
  final String? remainBytesIn;
  final String? remainBytesOut;
  final String? packetsOut;
  final String? packetsIn;
  final String? sessionTimeLeft;
  final String? uptime;
  final String action;

  LogoutResponse({
    required this.loggedIn,
    required this.linkLoginOnly,
    this.linkLogout,
    this.ip,
    this.bytesIn,
    this.bytesInNice,
    this.username,
    this.bytesOut,
    this.sps,
    this.bytesOutNice,
    this.remainBytesIn,
    this.remainBytesOut,
    this.packetsOut,
    this.packetsIn,
    this.sessionTimeLeft,
    this.uptime,
    required this.action,
  });

  factory LogoutResponse.fromJson(Map<String, dynamic> json) {
    return LogoutResponse(
      loggedIn: json['logged_in'] == 'yes',
      linkLoginOnly: json['link_login_only'] ?? '',
      linkLogout: json['link_logout'],
      ip: json['ip'],
      bytesIn: json['bytes_in'],
      bytesInNice: json['bytes_in_nice'],
      username: json['username'],
      bytesOut: json['bytes_out'],
      sps: json['sps'],
      bytesOutNice: json['bytes_out_nice'],
      remainBytesIn: json['remain_bytes_in'],
      remainBytesOut: json['remain_bytes_out'],
      packetsOut: json['packets_out'],
      packetsIn: json['packets_in'],
      sessionTimeLeft: json['session_time_left'],
      uptime: json['uptime'],
      action: json['action'] ?? '',
    );
  }
}

class SpeedOption {
  final String value;
  final String name;

  const SpeedOption({
    required this.value,
    required this.name,
  });

  static const List<SpeedOption> options = [
    SpeedOption(value: 'economic', name: 'سرعة اقتصادية'),
    SpeedOption(value: 'normal', name: 'سرعة متوسطة'),
    SpeedOption(value: 'high', name: 'سرعة عالية'),
    SpeedOption(value: 'very', name: 'سرعة مفتوحة'),
    SpeedOption(value: 'gaming', name: 'سرعة ألعاب أون لاين'),
  ];
}
